import { DbsApiGraphql, invokeAsyncLambda } from '@npco/bff-systemtest-utils';
import { ComponentClients } from '@npco/bff-systemtest-utils/dist/helper';
import { BaseTestHelper } from '@npco/bff-systemtest-utils/dist/helper/baseTestHelper';

export class DbsApiTestHelper extends BaseTestHelper {
  api: DbsApiGraphql;

  constructor() {
    super(ComponentClients.DeviceBackend, 'device');
    this.api = new DbsApiGraphql(this.appsyncClient, this.getEntityUuid());
  }

  before = async () => {
    await this.auth0Helper.setClient();
    await this.setAccessTokenInTestData();
    await this.setAppsyncTestData();
    await this.setSiteInTestData();
    await this.createTestCustomer();
    console.log('Test setup complete');
  };

  beforeAll = async () => {
    await this.auth0Helper.setClient();
    console.log('getting appsync stack');
    await this.setAccessTokenInTestData();
    await this.setAppsyncTestData();
    await Promise.all([this.setDeviceInTestData(), this.setSiteInTestData(), this.createTestCustomer()]);
    await this.createDomicileValue()
    await this.sleep();
    await this.assignDeviceToSite();
    await this.sleep();
    await this.createDeviceCacheSession();

    console.log('Test setup complete');
  };

  assignDeviceToSiteUuid = async (deviceUuid: string, siteUuid: string) => {
    console.log('assign device ', deviceUuid, ', to site ', siteUuid);
    await this.sendDeviceProjectionEvent('dbs.Device.Updated', {
      deviceUuid,
      siteUuid,
      entityUuid: this.getEntityUuid(),
    });
  };

  sendDeviceProjectionEvent = async (uri: string, payload: any) => {
    if (this.isDev()) {
      await this.publishEvent(uri, payload);
    } else {
      await this.invokeLambda('devices-deviceSettingsProjection', payload.deviceUuid, uri, payload);
    }
  };

  private readonly invokeLambda = async (functionName: string, id: string, uri: string, payload: any) => {
    return invokeAsyncLambda(`${this.getStage()}-dbs-api-${functionName}`, {
      version: 0,
      id,
      'detail-type': uri,
      source: 'mp',
      time: new Date().getTime(),
      detail: payload,
    });
  };
}
